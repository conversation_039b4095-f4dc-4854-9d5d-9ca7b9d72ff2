<template>
  <div class="app-container">
    <el-row :gutter="20" justify="center">
      <el-col :span="16">
        <el-card class="welcome-card">
          <div slot="header" class="clearfix">
            <span>欢迎使用考试系统</span>
            <el-button style="float: right; padding: 3px 0; font-size: 12px" type="text">系统版本：{{ version }}</el-button>
          </div>

          <div class="welcome-content">
            <div class="welcome-title">🎓 在线考试管理系统</div>
            <div class="welcome-desc">
              欢迎使用在线考试系统！请使用左侧菜单进行相关操作。
            </div>
            <div class="welcome-tips">
              <p>💡 <strong>提示：</strong></p>
              <ul>
                <li>管理员可以通过左侧菜单管理考试、题库和用户</li>
                <li>学员可以参加在线考试并查看成绩</li>
                <li>系统支持多种题型和智能评分</li>
              </ul>
            </div>
          </div>

        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import config from '../../../package.json'

export default {
  name: 'Dashboard',
  data() {
    return {
      currentRole: 'adminDashboard',
      version: config.version
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'siteData'
    ])
  },
  created() {

  }
}
</script>

<style scoped>
.welcome-card {
  max-width: 800px;
  margin: 0 auto;
  font-size: 14px;
  line-height: 1.6;
}

.welcome-content {
  text-align: center;
  padding: 20px;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

.welcome-desc {
  font-size: 16px;
  color: #606266;
  margin-bottom: 30px;
  line-height: 1.8;
}

.welcome-tips {
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.welcome-tips p {
  margin: 0 0 10px 0;
  font-weight: 600;
  color: #409eff;
}

.welcome-tips ul {
  margin: 0;
  padding-left: 20px;
}

.welcome-tips li {
  margin-bottom: 8px;
  color: #606266;
}
</style>
