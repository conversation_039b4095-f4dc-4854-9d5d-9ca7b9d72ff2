<template>
  <div class="app-container">
    <!-- 学员角色显示考试卡片 -->
    <div v-if="isStudent">
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="student-dashboard">
            <h2 class="dashboard-title">
              <i class="el-icon-user"></i>
              欢迎回来，{{ realName }}！
            </h2>

            <!-- 即将开始的考试 -->
            <div v-if="upcomingExam" class="upcoming-exam-section">
              <h3 class="section-title">
                <i class="el-icon-alarm-clock"></i>
                即将开始的考试
              </h3>
              <exam-card :exam="upcomingExam" />
            </div>

            <!-- 没有考试时的提示 -->
            <div v-else class="no-exam-section">
              <el-card class="no-exam-card">
                <div class="no-exam-content">
                  <i class="el-icon-document-checked no-exam-icon"></i>
                  <h3>暂无即将开始的考试</h3>
                  <p>请关注考试通知，或前往"在线考试"查看所有可参加的考试</p>
                  <el-button type="primary" @click="goToExamList">查看所有考试</el-button>
                </div>
              </el-card>
            </div>

            <!-- 快捷操作 -->
            <div class="quick-actions">
              <h3 class="section-title">
                <i class="el-icon-s-grid"></i>
                快捷操作
              </h3>
              <el-row :gutter="15">
                <el-col :span="8">
                  <el-card class="action-card" @click.native="goToExamList">
                    <div class="action-content">
                      <i class="el-icon-edit-outline action-icon"></i>
                      <span>在线考试</span>
                    </div>
                  </el-card>
                </el-col>
                <el-col :span="8">
                  <el-card class="action-card" @click.native="goToMyRecords">
                    <div class="action-content">
                      <i class="el-icon-trophy action-icon"></i>
                      <span>我的成绩</span>
                    </div>
                  </el-card>
                </el-col>
                <el-col :span="8">
                  <el-card class="action-card" @click.native="goToProfile">
                    <div class="action-content">
                      <i class="el-icon-user action-icon"></i>
                      <span>个人资料</span>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 非学员角色显示原有的欢迎页面 -->
    <el-row v-else :gutter="20" justify="center">
      <el-col :span="16">
        <el-card class="welcome-card">
          <div slot="header" class="clearfix">
            <span>欢迎使用考试系统</span>
            <el-button style="float: right; padding: 3px 0; font-size: 12px" type="text">系统版本：{{ version }}</el-button>
          </div>

          <div class="welcome-content">
            <div class="welcome-title">🎓 在线考试管理系统</div>
            <div class="welcome-desc">
              欢迎使用在线考试系统！请使用左侧菜单进行相关操作。
            </div>
            <div class="welcome-tips">
              <p>💡 <strong>提示：</strong></p>
              <ul>
                <li>管理员可以通过左侧菜单管理考试、题库和用户</li>
                <li>学员可以参加在线考试并查看成绩</li>
                <li>系统支持多种题型和智能评分</li>
              </ul>
            </div>
          </div>

        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import config from '../../../package.json'
import ExamCard from '@/components/ExamCard'
import { post } from '@/utils/request'

export default {
  name: 'Dashboard',
  components: {
    ExamCard
  },
  data() {
    return {
      currentRole: 'adminDashboard',
      version: config.version,
      upcomingExam: null,
      loading: false
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'siteData',
      'realName'
    ]),

    // 判断是否为学员角色
    isStudent() {
      return this.roles && this.roles.includes('student')
    }
  },
  created() {
    if (this.isStudent) {
      this.fetchUpcomingExam()
    }
  },
  methods: {
    // 获取即将开始的考试
    async fetchUpcomingExam() {
      try {
        this.loading = true
        const response = await post('/exam/api/exam/exam/online-paging', {
          current: 1,
          size: 10,
          params: {}
        })

        if (response.data && response.data.records && response.data.records.length > 0) {
          // 找到最近的可参加考试
          const exams = response.data.records
          const now = new Date().getTime()

          // 优先显示正在进行的考试
          let targetExam = exams.find(exam => {
            if (!exam.timeLimit) {
              return exam.state === 0 // 不限时且启用的考试
            }
            const startTime = new Date(exam.startTime).getTime()
            const endTime = new Date(exam.endTime).getTime()
            return now >= startTime && now <= endTime && exam.state === 0
          })

          // 如果没有正在进行的，找即将开始的
          if (!targetExam) {
            targetExam = exams.find(exam => {
              if (!exam.timeLimit) return false
              const startTime = new Date(exam.startTime).getTime()
              return now < startTime && exam.state === 0
            })
          }

          this.upcomingExam = targetExam
        }
      } catch (error) {
        console.error('获取考试信息失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 跳转到考试列表
    goToExamList() {
      this.$router.push({ name: 'ExamOnline' })
    },

    // 跳转到我的成绩
    goToMyRecords() {
      this.$router.push({ name: 'ListMyExam' })
    },

    // 跳转到个人资料
    goToProfile() {
      this.$router.push({ name: 'Profile' })
    }
  }
}
</script>

<style scoped>
/* 学员控制台样式 */
.student-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
}

.dashboard-title i {
  margin-right: 10px;
  color: #409eff;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 8px;
  color: #409eff;
}

.upcoming-exam-section {
  margin-bottom: 40px;
}

.no-exam-section {
  margin-bottom: 40px;
}

.no-exam-card {
  text-align: center;
  padding: 40px 20px;
}

.no-exam-content {
  color: #909399;
}

.no-exam-icon {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 20px;
}

.no-exam-content h3 {
  font-size: 20px;
  margin: 0 0 10px 0;
  color: #606266;
}

.no-exam-content p {
  font-size: 14px;
  margin: 0 0 20px 0;
  line-height: 1.6;
}

.quick-actions {
  margin-bottom: 30px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  padding: 20px 10px;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #606266;
}

.action-icon {
  font-size: 32px;
  margin-bottom: 10px;
  color: #409eff;
}

.action-content span {
  font-size: 14px;
  font-weight: 500;
}

/* 原有欢迎页面样式 */
.welcome-card {
  max-width: 800px;
  margin: 0 auto;
  font-size: 14px;
  line-height: 1.6;
}

.welcome-content {
  text-align: center;
  padding: 20px;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

.welcome-desc {
  font-size: 16px;
  color: #606266;
  margin-bottom: 30px;
  line-height: 1.8;
}

.welcome-tips {
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.welcome-tips p {
  margin: 0 0 10px 0;
  font-weight: 600;
  color: #409eff;
}

.welcome-tips ul {
  margin: 0;
  padding-left: 20px;
}

.welcome-tips li {
  margin-bottom: 8px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-title {
    font-size: 24px;
  }

  .action-card {
    margin-bottom: 15px;
  }

  .action-icon {
    font-size: 28px;
  }
}
</style>
