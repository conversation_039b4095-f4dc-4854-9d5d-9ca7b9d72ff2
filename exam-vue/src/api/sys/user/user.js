import { post } from '@/utils/request'

export function updateData(data) {
  return post('/exam/api/sys/user/update', data)
}

export function saveData(data) {
  return post('/exam/api/sys/user/save', data)
}

export function userReg(data) {
  return post('/exam/api/sys/user/reg', data)
}

/**
 * 获取用户详情
 * @param {string} id 用户ID
 */
export function fetchDetail(id) {
  return post('/exam/api/sys/user/detail', { id: id })
}

/**
 * 重置用户密码
 * @param {string} id 用户ID
 * @param {string} newPassword 新密码
 */
export function resetPassword(id, newPassword) {
  return post('/exam/api/sys/user/reset-password', { id: id, password: newPassword })
}
