<template>
  <el-card class="exam-card" :class="{ 'exam-card--disabled': !canStart }" @click.native="handleClick">
    <div class="exam-card__header">
      <div class="exam-card__status">
        <el-tag :type="statusType" size="small">{{ statusText }}</el-tag>
      </div>
      <div class="exam-card__time" v-if="exam.timeLimit">
        <i class="el-icon-time"></i>
        {{ formatTime(exam.startTime) }} - {{ formatTime(exam.endTime) }}
      </div>
      <div class="exam-card__time" v-else>
        <i class="el-icon-time"></i>
        不限时
      </div>
    </div>
    
    <div class="exam-card__content">
      <h3 class="exam-card__title">{{ exam.title }}</h3>
      <p class="exam-card__description">{{ exam.content || '暂无描述' }}</p>
      
      <div class="exam-card__info">
        <div class="exam-card__info-item">
          <span class="label">考试时长：</span>
          <span class="value">{{ exam.totalTime }}分钟</span>
        </div>
        <div class="exam-card__info-item">
          <span class="label">总分：</span>
          <span class="value">{{ exam.totalScore }}分</span>
        </div>
        <div class="exam-card__info-item">
          <span class="label">及格线：</span>
          <span class="value">{{ exam.qualifyScore }}分</span>
        </div>
      </div>
    </div>
    
    <div class="exam-card__footer">
      <el-button 
        :type="canStart ? 'primary' : 'info'" 
        :disabled="!canStart"
        size="medium"
        icon="el-icon-caret-right"
        @click.stop="handleStart"
      >
        {{ buttonText }}
      </el-button>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'ExamCard',
  props: {
    exam: {
      type: Object,
      required: true
    }
  },
  computed: {
    // 判断是否可以开始考试
    canStart() {
      if (!this.exam.timeLimit) {
        // 不限时的考试，只要状态是启用就可以开始
        return this.exam.state === 0
      }
      
      const now = new Date().getTime()
      const startTime = new Date(this.exam.startTime).getTime()
      const endTime = new Date(this.exam.endTime).getTime()
      
      // 在时间范围内且状态正常
      return now >= startTime && now <= endTime && this.exam.state === 0
    },
    
    // 状态文本
    statusText() {
      if (!this.exam.timeLimit) {
        return this.exam.state === 0 ? '可参加' : '已禁用'
      }
      
      const now = new Date().getTime()
      const startTime = new Date(this.exam.startTime).getTime()
      const endTime = new Date(this.exam.endTime).getTime()
      
      if (now < startTime) {
        return '未开始'
      } else if (now > endTime) {
        return '已结束'
      } else if (this.exam.state === 0) {
        return '进行中'
      } else {
        return '已禁用'
      }
    },
    
    // 状态类型
    statusType() {
      if (this.canStart) {
        return 'success'
      } else if (this.statusText === '未开始') {
        return 'warning'
      } else {
        return 'info'
      }
    },
    
    // 按钮文本
    buttonText() {
      if (this.canStart) {
        return '开始考试'
      } else if (this.statusText === '未开始') {
        return '等待开始'
      } else if (this.statusText === '已结束') {
        return '考试已结束'
      } else {
        return '暂不可用'
      }
    }
  },
  methods: {
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    },
    
    // 处理卡片点击
    handleClick() {
      if (this.canStart) {
        this.handleStart()
      }
    },
    
    // 处理开始考试
    handleStart() {
      if (this.canStart) {
        this.$router.push({ 
          name: 'PreExam', 
          params: { examId: this.exam.id } 
        })
      }
    }
  }
}
</script>

<style scoped>
.exam-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 20px;
}

.exam-card:hover:not(.exam-card--disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  border-color: #409eff;
}

.exam-card--disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
}

.exam-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.exam-card__time {
  font-size: 14px;
  opacity: 0.9;
}

.exam-card__time i {
  margin-right: 5px;
}

.exam-card__content {
  margin-bottom: 20px;
}

.exam-card__title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: white;
}

.exam-card__description {
  font-size: 14px;
  opacity: 0.9;
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.exam-card__info {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.exam-card__info-item {
  font-size: 14px;
}

.exam-card__info-item .label {
  opacity: 0.8;
}

.exam-card__info-item .value {
  font-weight: 600;
  margin-left: 5px;
}

.exam-card__footer {
  text-align: right;
}

.exam-card__footer .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.exam-card__footer .el-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.exam-card__footer .el-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.6);
}
</style>
