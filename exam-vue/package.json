{"name": "yf-exam-lite", "version": "1.9.2", "description": "云帆学习考试系统", "author": "<EMAIL>", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:demo": "vue-cli-service build --mode demo", "lint": "eslint --ext .js,.vue src", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"axios": "^0.21.1", "babel-plugin-dynamic-import-node": "^2.3.3", "clipboard": "^2.0.4", "cos-js-sdk-v5": "^1.2.16", "dropzone": "5.5.1", "element-ui": "^2.15.7", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "moment": "^2.29.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "querystring": "^0.2.0", "screenfull": "^4.2.0", "showdown": "^1.9.1", "sortablejs": "^1.8.4", "tracking": "^1.1.3", "vue": "2.6.10", "vue-count-to": "1.0.13", "vue-fullscreen": "^2.1.6", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vue-uuid": "^2.0.2", "vue-visibility-change": "^1.2.1", "vuedraggable": "2.20.0", "vuex": "3.1.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-service": "^4.2.2", "autoprefixer": "^9.5.1", "babel-eslint": "10.0.1", "babel-jest": "^25.1.0", "chalk": "2.4.2", "connect": "3.6.6", "sass": "^1.49.9", "sass-loader": "^10.2.0", "script-ext-html-webpack-plugin": "^2.1.5", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.10", "webpack": "^4.46.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}