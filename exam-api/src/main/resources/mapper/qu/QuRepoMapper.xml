<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.qu.mapper.QuRepoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.qu.entity.QuRepo">
        <id column="id" property="id" />
        <result column="qu_id" property="quId" />
        <result column="repo_id" property="repoId" />
        <result column="qu_type" property="quType" />
        <result column="sort" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`qu_id`,`repo_id`,`qu_type`,`sort`
    </sql>


</mapper>
